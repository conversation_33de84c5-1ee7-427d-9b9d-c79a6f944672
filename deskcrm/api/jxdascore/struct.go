package jxdascore

// StudentLessonScore 学生章节学分信息
type StudentLessonScore struct {
	BizId     int64 `json:"bizId"`     // 章节ID
	ObjId     int64 `json:"objId"`     // 学生ID
	Score     int   `json:"score"`     // 学分
	BizLevel  int   `json:"bizLevel"`  // 业务级别
	BizPid    int   `json:"bizPid"`    // 业务父ID
	ProductId int   `json:"productId"` // 产品ID
}

// GetStudentLessonsScoreResponse 获取学生多章节学分响应
type GetStudentLessonsScoreResponse []StudentLessonScore

// GetStudentsLessonScoreResponse 获取多学生单章节学分响应
type GetStudentsLessonScoreResponse []StudentLessonScore
