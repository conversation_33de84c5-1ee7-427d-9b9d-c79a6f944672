package jxdascore

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"strconv"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// Client jxdascore API客户端
type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.JxDaScore,
	}
	return c
}

const (
	getStudentLessonsScoreApi = "/jxdascore/score/getobjbizscorelist" // 获取学生章节学分
	getStudentsLessonScoreApi = "/jxdascore/score/getbizobjscorelist" // 获取多学生单章节学分
	
	// API常量，对应PHP中的配置
	apiMaxNum = 50
)

// GetStudentLessonsScore 获取学生多章节学分
// 对应PHP的 Api_Jxdascore::getStudentLessonsScore 方法
func (c *Client) GetStudentLessonsScore(ctx *gin.Context, studentUid int64, lessonIds []int64) (map[int64]*StudentLessonScore, error) {
	if studentUid <= 0 || len(lessonIds) == 0 {
		zlog.Warnf(ctx, "GetStudentLessonsScore invalid params: studentUid=%d, lessonIds=%v", studentUid, lessonIds)
		return make(map[int64]*StudentLessonScore), nil
	}

	result := make(map[int64]*StudentLessonScore)

	// 分批处理，每批最多50个lessonId
	batchSize := apiMaxNum
	for i := 0; i < len(lessonIds); i += batchSize {
		end := min(i+batchSize, len(lessonIds))
		batchLessonIds := lessonIds[i:end]

		// 构建请求参数
		bizIds := make([]string, len(batchLessonIds))
		for j, id := range batchLessonIds {
			bizIds[j] = strconv.FormatInt(id, 10)
		}

		req := map[string]interface{}{
			"bizIds":    bizIds,
			"bizLevel":  2,
			"bizPid":    -1,
			"productId": 100001,
			"objId":     studentUid,
		}

		// 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		// 发送请求
		res, err := c.cli.HttpPost(ctx, getStudentLessonsScoreApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "GetStudentLessonsScore request err: %v", err)
			return nil, err
		}

		// 解析响应
		var apiResp GetStudentLessonsScoreResponse
		_, err = api.DecodeResponse(ctx, res, &apiResp)
		if err != nil {
			zlog.Warnf(ctx, "GetStudentLessonsScore decode response err: %v", err)
			return nil, err
		}

		// 处理返回数据
		for _, item := range apiResp {
			result[item.BizId] = &item
		}
	}

	return result, nil
}

// GetStudentsLessonScore 获取多学生单章节学分
// 对应PHP的 Api_Jxdascore::getStudentsLessonScore 方法
func (c *Client) GetStudentsLessonScore(ctx *gin.Context, studentUids []int64, lessonId int64) (map[int64]*StudentLessonScore, error) {
	if len(studentUids) == 0 || lessonId <= 0 {
		zlog.Warnf(ctx, "GetStudentsLessonScore invalid params: studentUids=%v, lessonId=%d", studentUids, lessonId)
		return make(map[int64]*StudentLessonScore), nil
	}

	result := make(map[int64]*StudentLessonScore)

	// 分批处理，每批最多50个studentUid
	batchSize := apiMaxNum
	for i := 0; i < len(studentUids); i += batchSize {
		end := min(i+batchSize, len(studentUids))
		batchStudentUids := studentUids[i:end]

		// 构建请求参数
		objIds := make([]string, len(batchStudentUids))
		for j, uid := range batchStudentUids {
			objIds[j] = strconv.FormatInt(uid, 10)
		}

		req := map[string]interface{}{
			"bizId":     lessonId,
			"bizLevel":  2,
			"bizPid":    -1,
			"productId": 100001,
			"objIds":    objIds,
		}

		// 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		// 发送请求
		res, err := c.cli.HttpPost(ctx, getStudentsLessonScoreApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "GetStudentsLessonScore request err: %v", err)
			return nil, err
		}

		// 解析响应
		var apiResp GetStudentsLessonScoreResponse
		_, err = api.DecodeResponse(ctx, res, &apiResp)
		if err != nil {
			zlog.Warnf(ctx, "GetStudentsLessonScore decode response err: %v", err)
			return nil, err
		}

		// 处理返回数据
		for _, item := range apiResp {
			result[item.ObjId] = &item
		}
	}

	return result, nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
