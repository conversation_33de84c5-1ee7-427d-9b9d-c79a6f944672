package pcassistant

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"encoding/json"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.PcAssistant,
	}
	return c
}

const (
	LessonCompositionConfAPI = "/pcassistant/api/composition/getcompositiontid"
)

// GetLessonCompositionConf 获取章节巩固练习作文配置信息
// 对应PHP中的Api_PcAssistant::lessonCompositionConf方法
func (c *Client) GetLessonCompositionConf(ctx *gin.Context, lessonIds []int64) (map[int64]*CompositionConf, error) {
	if len(lessonIds) == 0 {
		return make(map[int64]*CompositionConf), nil
	}

	result := make(map[int64]*CompositionConf)

	// 按照PHP逻辑，每次最多处理20个lessonId
	chunks := chunkInt64Slice(lessonIds, 20)
	
	for _, chunk := range chunks {
		lessonIdsJson, err := json.Marshal(chunk)
		if err != nil {
			zlog.Warnf(ctx, "GetLessonCompositionConf marshal lessonIds failed: %v", err)
			continue
		}

		params := map[string]interface{}{
			"lessonIds": string(lessonIdsJson),
		}

		opts := base.HttpRequestOptions{RequestBody: params, Encode: base.EncodeForm}
		utils.DecorateHttpOptions(ctx, &opts)

		res, err := c.cli.HttpPost(ctx, LessonCompositionConfAPI, opts)
		if err != nil {
			zlog.Warnf(ctx, "GetLessonCompositionConf request failed: %v", err)
			continue
		}

		if err = api.ApiHttpCode(ctx, res); err != nil {
			zlog.Warnf(ctx, "GetLessonCompositionConf api error: %v", err)
			continue
		}

		var apiResp map[int64]*CompositionConf
		if _, err = api.DecodeResponse(ctx, res, &apiResp); err != nil {
			zlog.Warnf(ctx, "GetLessonCompositionConf decode failed: %v", err)
			continue
		}

		// 合并结果
		for lessonId, conf := range apiResp {
			result[lessonId] = conf
		}
	}

	return result, nil
}

// chunkInt64Slice 将int64切片分块
func chunkInt64Slice(slice []int64, chunkSize int) [][]int64 {
	var chunks [][]int64
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}
