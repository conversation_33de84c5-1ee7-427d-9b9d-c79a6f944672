package dataQuery

/*
*
合并的几个条件：
- 主键完全一样，包括顺序（zlink任务里定义的顺序）
- 数据量级
- 重要级别
满足上述条件后：
- 确认一下当前es已有几个同步任务，超过5个后最好开新的es，避免冲突频繁造成数据延迟
- 确认一下当前es字段列有多少，超过上百个字段后，尽量考虑一下是否需要开新的es，避免es太大影响其他字段列查询

es命名：
- adl开头：聚合数据
- idl开头：明细数据
*/
const (
	DataSourceDimUd                                = "dimUD"
	DataSourceUOn                                  = "uOn"
	DataSourceInteractQuestion                     = "liveInteractQuestion"
	DataSourceCu                                   = "cu"
	DataSourceCommonLu                             = "commonLu"
	DataSourceCommonCu                             = "commonCu"
	DataSourceLu                                   = "lu"
	DataAssistantCourseLu                          = "assistantCourseLu"
	DataAssistantCourseCu                          = "assistantCourseCu"
	DataSourceLpcLeads                             = "lpcLeads"
	DataSourceLessonStudent                        = "tblLessonStudent"
	DataSourceCustomTag                            = "customTag"
	DataListByCourseIdLessonIdsAssistantUid        = "getListByCourseIdLessonIdsAssistantUid"
	DataListByBindIdsBindTypeRelationTypesExamTags = "getListByBindIdsBindTypeRelationTypesExamTags"
	DataSourceLpcLu                                = "lpcLu"
	DataSourceTblPreOrderList                      = "tblPreOrderList"
	DataSourceDauStudentInfo                       = "dauStudentInfo"
	DataSourceLeads                                = "leads"
	DataSourceLeadsExt                             = "leadsExt"
	DataSourceCuOrder                              = "cuOrder"
	DataSourceCuByCuOrder                          = "cuByCuOrder"
	CourseLessonCommonDataNew                      = "courseLessonCommonDataNew"
	DataSourceStudentAppData                       = "studentAppData"
	DsIdlU                                         = "u" // u维度 明细数据
	DataSourceSopCourseStudentCu                   = "courseStudentAggr"
	DataPublicSeaClueInfo                          = "publicSeaClueInfo"
	DataSourceApiLessonReport                      = "ApiLessonReport "
	DataSourceUnitStudent                          = "unitStudent "
	DataSourceStudentExprCnt                       = "studentExprCnt"
	DataSourceStudentOrderInfo                     = "studentOrder"
	DataSourceExamRelation                         = "examRelation"
	DataSourceLesson                               = "lesson"
	DataSourceScore                                = "score"
)
