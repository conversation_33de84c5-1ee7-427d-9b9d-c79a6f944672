package dataQuery

import (
	"deskcrm/api/jxdascore"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// GetStudentLessonsScore 获取学生多章节学分信息
// 对应PHP中的 Api_Jxdascore::getStudentLessonsScore 方法
func (s *Singleton) GetStudentLessonsScore(ctx *gin.Context, studentUid int64, lessonIds []int64) (map[int64]*jxdascore.StudentLessonScore, error) {
	if studentUid <= 0 || len(lessonIds) == 0 {
		return make(map[int64]*jxdascore.StudentLessonScore), nil
	}

	client := jxdascore.NewClient()
	data, err := client.GetStudentLessonsScore(ctx, studentUid, lessonIds)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentLessonsScore failed: %v", err)
		return nil, err
	}

	return data, nil
}

// GetStudentsLessonScore 获取多学生单章节学分信息
// 对应PHP中的 Api_Jxdascore::getStudentsLessonScore 方法
func (s *Singleton) GetStudentsLessonScore(ctx *gin.Context, studentUids []int64, lessonId int64) (map[int64]*jxdascore.StudentLessonScore, error) {
	if len(studentUids) == 0 || lessonId <= 0 {
		return make(map[int64]*jxdascore.StudentLessonScore), nil
	}

	client := jxdascore.NewClient()
	data, err := client.GetStudentsLessonScore(ctx, studentUids, lessonId)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentsLessonScore failed: %v", err)
		return nil, err
	}

	return data, nil
}
