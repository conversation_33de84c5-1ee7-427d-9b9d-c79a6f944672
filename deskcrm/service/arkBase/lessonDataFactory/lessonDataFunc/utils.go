package lessonDataFunc

import (
	"deskcrm/api/zbcore"
	"deskcrm/conf"
	"fmt"
	"net/url"
	"strconv"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// formatDuration 格式化时长为"XminYs"格式
// 对应PHP中的AssistantDesk_Tools::formatDurationTime方法
func FormatDuration(seconds int64) string {
	remainingSeconds := seconds % 60
	minutes := seconds / 60
	retTime := fmt.Sprintf("%dmin", minutes)
	if remainingSeconds > 0 {
		retTime = retTime + fmt.Sprintf("%ds", remainingSeconds)
	}
	return retTime
}

// LessonDataArray 创建新的课程数据数组，对应PHP中的数组格式 [显示文本, 颜色, 是否可点击]
// 例如：$row['preview'] = ['-', 'gray', 1]
func LessonDataArray(text, color string, clickable int) []interface{} {
	return []interface{}{text, color, clickable}
}

// GenerateMonthlyExamReportUrl 生成月考报告URL
// 对应PHP中的Service_Data_MonthlyExamReport::getReportUrl方法
func GenerateMonthlyExamReportUrl(ctx *gin.Context, studentUid, courseId, lessonId int64) (string, error) {
	if studentUid <= 0 || courseId <= 0 || lessonId <= 0 {
		return "", nil
	}

	// 获取课程信息以获取mainSubjectId
	courseFields := []string{"courseId", "mainSubjectId"}

	// 直接调用底层API获取课程信息
	app := conf.GetAppName()
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, "dal", "course", "getKV", false, app)
	arrParams := map[string]interface{}{
		"courseIds":    []int64{courseId},
		"courseFields": courseFields,
	}

	var output map[string]map[string]interface{}
	_, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
	if err != nil {
		zlog.Warnf(ctx, "GenerateMonthlyExamReportUrl PostDal failed: %v", err)
		return "", err
	}

	courseIdStr := cast.ToString(courseId)
	courseData, exists := output[courseIdStr]
	if !exists {
		zlog.Warnf(ctx, "GenerateMonthlyExamReportUrl course not found: %d", courseId)
		return "", nil
	}

	// 构建查询参数
	queryParams := url.Values{}
	queryParams.Set("studentId", strconv.FormatInt(studentUid, 10))
	queryParams.Set("courseId", strconv.FormatInt(courseId, 10))
	queryParams.Set("lessonId", strconv.FormatInt(lessonId, 10))

	// 构建基础URL - 对应PHP中的AssistantDesk_Config::PUBLIC_ADDRESS
	// TODO: 这里应该从配置文件中获取实际的公共地址
	baseUrl := "https://www.zuoyebang.com"
	reportUrl := fmt.Sprintf("%s/assistantweb/wxview/exam-report?%s", baseUrl, queryParams.Encode())

	// 添加学科ID参数
	mainSubjectId := cast.ToInt64(courseData["mainSubjectId"])
	if mainSubjectId > 0 {
		reportUrl += "&subjectId=" + strconv.FormatInt(mainSubjectId, 10)
	}

	return reportUrl, nil
}
